// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'certificate_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$certificateListControllerHash() =>
    r'750d8b1043399b662285779a745e11aa084bd6e6';

/// This controller is an [AsyncNotifier] that holds and handles certificate list state
///
/// Copied from [CertificateListController].
@ProviderFor(CertificateListController)
final certificateListControllerProvider = AutoDisposeAsyncNotifierProvider<
  CertificateListController,
  CertificateListState
>.internal(
  CertificateListController.new,
  name: r'certificateListControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$certificateListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CertificateListController =
    AutoDisposeAsyncNotifier<CertificateListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
