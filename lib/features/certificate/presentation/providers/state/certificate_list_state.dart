import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/entities/certificate.dart';
import '../../../domain/entities/certificate_level.dart';

part 'certificate_list_state.freezed.dart';

@freezed
sealed class CertificateListState with _$CertificateListState {
  const factory CertificateListState({
    @Default({}) Map<CertificateLevel, List<Certificate>> groupedCertificates,
  }) = _CertificateListState;
}
