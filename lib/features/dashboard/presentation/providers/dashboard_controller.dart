import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/dashboard/presentation/providers/state/dashboard_state.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

part 'dashboard_controller.g.dart';

@riverpod
class DashboardController extends _$DashboardController {
  late final UserDataServiceRepository userDataServiceRepository;
  late final MainLessonRepository mainLessonRepository;
  late final MainLessonStateNotifier mainLessonStateNotifier;
  late final ErrorHandler errorHandler;

  @override
  FutureOr<DashboardState> build() {
    userDataServiceRepository = ref.watch(userDataServiceProvider);
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    errorHandler = ErrorHandler(ref.read(crashlyticsServiceProvider));
    init();
    return DashboardState();
  }

  Future<void> init() async {
    if (FirebaseAuth.instance.currentUser == null) return;
    ref.read(crashlyticsServiceProvider).log('Initializing dashboard');
    await errorHandler.handleAsync(
      () => _loadUserData(),
      context: 'Dashboard Initialization',
      fatal: false,
    );
  }

  Future<void> _loadUserData() async {
    final result = await errorHandler.handleAsync(
      () => userDataServiceRepository.getUserData(),
      context: 'Loading User Data',
      fatal: false,
    );

    if (result == null) return;

    result.fold(
      (failure) => state = AsyncError(failure.message, StackTrace.empty),
      (data) async {
        final updatedState = state.value!.copyWith(
          lastPronunciation: data.lastPronunciation,
          lastConversation: data.lastConversation,
          lastListening: data.lastListening,
          lastSpeaking: data.lastSpeaking,
          afterTest: data.afterTest,
        );
        state = AsyncData(updatedState);

        // Set user context for crash reporting
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics.setUserId(FirebaseAuth.instance.currentUser!.uid);
        await crashlytics.setUserEmail(data.email);
        crashlytics.log('User data loaded successfully');

        final lastCourse = await _processLastCourses(data);

        if (lastCourse.isNotEmpty) {
          lastCourse.sort(
            (a, b) => b.info.accessTime.compareTo(a.info.accessTime),
          );
          state = AsyncData(updatedState.copyWith(lastCourse: lastCourse));
        } else {
          await getFirstLesson();
        }
      },
    );
  }

  Future<List<LastCourseInfo>> _processLastCourses(UserData data) async {
    final List<LastCourseInfo> lastCourse = [];
    final mainLessonState = ref.watch(mainLessonStateProvider.notifier);

    final futures = <Future<void>>[];
    final coursesToProcess = [
      (data.lastPronunciation, mainLessonState.updateLastPronunciation),
      (data.lastConversation, mainLessonState.updateLastConversation),
      (data.lastListening, mainLessonState.updateLastListening),
      (data.lastSpeaking, mainLessonState.updateLastSpeaking),
    ];

    for (final (course, updateFunction) in coursesToProcess) {
      if (course != null) {
        futures.add(
          getLastCourseData(course).then((res) {
            if (res != null) {
              lastCourse.add(res);
              updateFunction(course);
            }
          }),
        );
      }
    }

    await Future.wait(futures);
    return lastCourse;
  }

  Future<LastCourseInfo?> getLastCourseData(LastCourse lastCourse) async {
    final result = await errorHandler.handleAsync(
      () => switch (lastCourse.section) {
        SectionType.conversation => mainLessonRepository.getConversation(
          path: lastCourse.path,
        ),
        SectionType.listening => mainLessonRepository.getListening(
          path: lastCourse.path,
        ),
        SectionType.speaking => mainLessonRepository.getSpeaking(
          path: lastCourse.path,
        ),
        SectionType.pronunciation => mainLessonRepository.getPronunciation(
          path: lastCourse.path,
        ),
      },
      context: 'Get Last Course Data',
      fatal: false,
    );

    if (result == null) return null;

    return result.fold((failure) {
      state = AsyncError(failure.message, StackTrace.empty);
      return null;
    }, (data) => LastCourseInfo(info: lastCourse, data: data));
  }

  Future<void> getFirstLesson() async {
    final result = await errorHandler.handleAsync(
      () => mainLessonRepository.getFirstLesson(),
      context: 'Get First Lesson',
      fatal: false,
    );

    if (result == null) return;

    result.fold(
      (failure) => state = AsyncError(failure.message, StackTrace.empty),
      (data) => state = AsyncData(state.value!.copyWith(firstLesson: data)),
    );
  }

  void changeisFromLastCourse(bool fromLastCourse) {
    mainLessonStateNotifier.updateFromLastCourse(fromLastCourse);
  }
}
