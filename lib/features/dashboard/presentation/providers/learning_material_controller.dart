import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/dashboard/presentation/providers/state/learning_material_state.dart';
import 'package:selfeng/features/dashboard/presentation/providers/dashboard_controller.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

part 'learning_material_controller.g.dart';

@riverpod
class LearningMaterialController extends _$LearningMaterialController {
  @override
  FutureOr<LearningMaterialState> build() {
    _loadChapters();
    return LearningMaterialState();
  }

  Future<void> _loadChapters() async {
    state = const AsyncLoading();

    // Set Crashlytics context
    final crashlytics = ref.read(crashlyticsServiceProvider);
    await crashlytics.setCustomKeys({
      'feature': 'learning_material',
      'controller': 'learning_material_controller',
    });

    // Watch the dashboard state to get lastCourse data
    final dashboardState = ref.watch(dashboardControllerProvider);

    // Determine the fetched level based on lastCourse data
    final fetchedLevel = await _determineFetchedLevel(
      dashboardState.value?.lastCourse ?? [],
    );

    // Get repository reference when needed
    final mainLessonRepository = ref.read(mainLessonRepositoryProvider);
    final chapters = await mainLessonRepository.getChapters(fetchedLevel);

    chapters.fold(
      (failure) {
        final crashlytics = ref.read(crashlyticsServiceProvider);
        crashlytics.recordError(
          Exception(failure.message),
          StackTrace.empty,
          reason: 'Failed to load chapters',
          context: {
            'fetched_level': fetchedLevel.toString(),
            'feature': 'learning_material',
          },
        );
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) =>
          state = AsyncData(
            LearningMaterialState(chapters: data, fetchedLevel: fetchedLevel),
          ),
    );
  }

  Future<Level> _determineFetchedLevel(List<LastCourseInfo> lastCourse) async {
    // Default level is A1
    if (lastCourse.isEmpty) {
      return Level.a1;
    }

    // Group the data by level
    final Map<Level, int> levelCounts = {};

    for (final courseInfo in lastCourse) {
      try {
        final level = Level.values.byName(courseInfo.info.level.toLowerCase());
        levelCounts[level] = (levelCounts[level] ?? 0) + 1;
      } catch (e, stackTrace) {
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics.recordError(
          e,
          stackTrace,
          reason: 'Invalid level in course info',
          context: {
            'invalid_level': courseInfo.info.level,
            'feature': 'learning_material',
            'operation': 'determine_fetched_level',
          },
        );
        // Skip entries with invalid level strings
      }
    }

    if (levelCounts.isEmpty) {
      return Level.a1;
    }

    // Find the maximum count first
    final maxCount = levelCounts.values.reduce((a, b) => a > b ? a : b);

    // Get all levels with the maximum count
    final levelsWithMaxCount =
        levelCounts.entries
            .where((entry) => entry.value == maxCount)
            .map((entry) => entry.key)
            .toList();

    // If there's only one level with max count, return it
    if (levelsWithMaxCount.length == 1) {
      return levelsWithMaxCount.first;
    }

    // If there are multiple levels with the same max count, return the lowest level
    // Level order: A1, A2, B1, B2, C1, C2 (by index)
    levelsWithMaxCount.sort((a, b) => a.index.compareTo(b.index));
    return levelsWithMaxCount.first;
  }

  void changeisFromLastCourse(bool fromLastCourse) {
    final mainLessonStateNotifier = ref.read(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateFromLastCourse(fromLastCourse);
  }
}
