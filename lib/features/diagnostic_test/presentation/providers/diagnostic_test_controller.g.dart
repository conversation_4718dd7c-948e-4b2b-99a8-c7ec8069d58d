// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'diagnostic_test_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$diagnosticTestControllerHash() =>
    r'3a9c3c296fc232bac6d75f37517380d197adb17a';

/// This controller is an [AsyncNotifier] that holds and handles our diagnostic test state
///
/// Copied from [DiagnosticTestController].
@ProviderFor(DiagnosticTestController)
final diagnosticTestControllerProvider = AutoDisposeAsyncNotifierProvider<
  DiagnosticTestController,
  DiagnosticTestState
>.internal(
  DiagnosticTestController.new,
  name: r'diagnosticTestControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$diagnosticTestControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DiagnosticTestController =
    AutoDisposeAsyncNotifier<DiagnosticTestState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
