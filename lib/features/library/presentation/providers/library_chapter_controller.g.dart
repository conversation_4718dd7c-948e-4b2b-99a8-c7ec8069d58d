// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'library_chapter_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$libraryChapterControllerHash() =>
    r'a9b11d8da1e849df264984a3014663206f9f408c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$LibraryChapterController
    extends BuildlessAutoDisposeAsyncNotifier<LibraryChapterState> {
  late final String level;

  FutureOr<LibraryChapterState> build(String level);
}

/// See also [LibraryChapterController].
@ProviderFor(LibraryChapterController)
const libraryChapterControllerProvider = LibraryChapterControllerFamily();

/// See also [LibraryChapterController].
class LibraryChapterControllerFamily
    extends Family<AsyncValue<LibraryChapterState>> {
  /// See also [LibraryChapterController].
  const LibraryChapterControllerFamily();

  /// See also [LibraryChapterController].
  LibraryChapterControllerProvider call(String level) {
    return LibraryChapterControllerProvider(level);
  }

  @override
  LibraryChapterControllerProvider getProviderOverride(
    covariant LibraryChapterControllerProvider provider,
  ) {
    return call(provider.level);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'libraryChapterControllerProvider';
}

/// See also [LibraryChapterController].
class LibraryChapterControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          LibraryChapterController,
          LibraryChapterState
        > {
  /// See also [LibraryChapterController].
  LibraryChapterControllerProvider(String level)
    : this._internal(
        () => LibraryChapterController()..level = level,
        from: libraryChapterControllerProvider,
        name: r'libraryChapterControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$libraryChapterControllerHash,
        dependencies: LibraryChapterControllerFamily._dependencies,
        allTransitiveDependencies:
            LibraryChapterControllerFamily._allTransitiveDependencies,
        level: level,
      );

  LibraryChapterControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.level,
  }) : super.internal();

  final String level;

  @override
  FutureOr<LibraryChapterState> runNotifierBuild(
    covariant LibraryChapterController notifier,
  ) {
    return notifier.build(level);
  }

  @override
  Override overrideWith(LibraryChapterController Function() create) {
    return ProviderOverride(
      origin: this,
      override: LibraryChapterControllerProvider._internal(
        () => create()..level = level,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        level: level,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    LibraryChapterController,
    LibraryChapterState
  >
  createElement() {
    return _LibraryChapterControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LibraryChapterControllerProvider && other.level == level;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, level.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LibraryChapterControllerRef
    on AutoDisposeAsyncNotifierProviderRef<LibraryChapterState> {
  /// The parameter `level` of this provider.
  String get level;
}

class _LibraryChapterControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          LibraryChapterController,
          LibraryChapterState
        >
    with LibraryChapterControllerRef {
  _LibraryChapterControllerProviderElement(super.provider);

  @override
  String get level => (origin as LibraryChapterControllerProvider).level;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
