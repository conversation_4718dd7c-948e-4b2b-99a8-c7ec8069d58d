import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/conversation_state.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:video_player/video_player.dart';

part 'conversation_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class ConversationController extends _$ConversationController {
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  late ChapterContentStateNotifier _chapterContentStateNotifier;
  late final dynamic crashlyticsService;

  List<ContentIndexData> _paths = [];
  Set<String> _completedPaths = {};
  @override
  late String localName;

  late MainLessonState _mainLessonState;

  @override
  FutureOr<ConversationState> build(
    String level,
    String chapter,
    String path,
    String localName,
  ) async {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );
    crashlyticsService = ref.read(crashlyticsServiceProvider);

    // Set custom keys for better error context
    await crashlyticsService.setCustomKeys({
      'feature': 'main_lesson',
      'section': 'conversation',
      'level': level,
      'chapter': chapter,
    });

    try {
      final result = await mainLessonRepository.isIntro(
        lessonName: 'conversation',
      );
      result.fold(
        (failure) async {
          await crashlyticsService.recordError(
            failure,
            StackTrace.current,
            reason: 'Failed to check intro status',
            context: {'lessonName': 'conversation', 'operation': 'isIntro'},
            fatal: false,
          );
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          this.localName = localName;
        },
      );
    } catch (error, stackTrace) {
      await crashlyticsService.recordError(
        error,
        stackTrace,
        reason: 'Unexpected error checking intro status',
        context: {'lessonName': 'conversation', 'operation': 'isIntro'},
        fatal: false,
      );
      state = AsyncError(error.toString(), stackTrace);
    }
    init(level, chapter, path);
    return ConversationState(
      flickManager: FlickManager(
        videoPlayerController: VideoPlayerController.networkUrl(
          Uri.parse(
            'https://firebasestorage.googleapis.com/v0/b/selfeng-dev.appspot.com/o/video%2Ftmp%2Ftmpvideo.mp4?alt=media&token=423dc215-6ccf-4c16-9971-a6306f998f10',
          ),
        ),
      ),
    );
  }

  // Helper method to load subtitle file
  Future<ClosedCaptionFile> _loadSubtitleFile(String subtitleUrl) async {
    try {
      Dio dio = Dio();
      final response = await dio.get(
        subtitleUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      final fileContents = utf8.decode(response.data);
      // Determine subtitle format based on file extension
      return SubRipCaptionFile(fileContents);
    } catch (e, stackTrace) {
      await crashlyticsService.recordError(
        e,
        stackTrace,
        reason: 'Failed to load subtitle file',
        context: {
          'subtitleUrl': subtitleUrl,
          'operation': '_loadSubtitleFile',
          'errorType': e.runtimeType.toString(),
        },
        fatal: false,
      );
      debugPrint('Failed to load subtitle file: $e');
      // Return empty caption file
      return WebVTTCaptionFile('');
    }
  }

  Future<void> init(String level, String chapter, String path) async {
    try {
      final contents = await mainLessonRepository.getPathIndex(
        level: level,
        chapter: chapter,
        section: SectionType.conversation,
      );

      await mainLessonRepository.saveIntro(lessonName: 'conversation');

      contents.fold(
        (failure) async {
          await crashlyticsService.recordError(
            failure,
            StackTrace.current,
            reason: 'Failed to get path index',
            context: {
              'level': level,
              'chapter': chapter,
              'section': 'conversation',
              'operation': 'getPathIndex',
            },
            fatal: false,
          );
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          _paths = data;

          final result = await _userDataServiceRepository.getConversationResult(
            PronunciationScoreParams(level: level, chapter: chapter),
          );

          result.fold(
            (failure) async {
              await crashlyticsService.recordError(
                failure,
                StackTrace.current,
                reason: 'Failed to get conversation results',
                context: {
                  'level': level,
                  'chapter': chapter,
                  'operation': 'getConversationResult',
                },
                fatal: false,
              );
              state = AsyncError(failure.message, StackTrace.current);
            },
            (data) {
              _completedPaths = data.map((e) => e.path).toSet();
            },
          );

          _mainLessonState = ref.read(mainLessonStateProvider);
          if (path != 'blankpath') {
            final contentPath = utf8.decode(base64Url.decode(path));
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: _paths.indexWhere(
                  (element) => element.contentPath == contentPath,
                ),
              ),
            );
          } else if (_mainLessonState.fromLastCourse == true &&
              _mainLessonState.lastConversation != null) {
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: _paths.indexWhere(
                  (element) =>
                      element.contentPath ==
                      _mainLessonState.lastConversation!.path,
                ),
              ),
            );
          }

          initContent();
        },
      );
    } catch (error, stackTrace) {
      await crashlyticsService.recordError(
        error,
        stackTrace,
        reason: 'Unexpected error during initialization',
        context: {
          'level': level,
          'chapter': chapter,
          'path': path,
          'operation': 'init',
        },
        fatal: false,
      );
      state = AsyncError(error.toString(), stackTrace);
    }
  }

  Future<void> initContent() async {
    try {
      final contentFuture = mainLessonRepository.getConversationList(
        _paths.map((e) => e.contentPath).toList(),
      );
      final bookmarkFuture = _userDataServiceRepository.getBookmarksBySection(
        section: SectionType.conversation,
      );

      final results = await Future.wait([contentFuture, bookmarkFuture]);

      final contentResult =
          results[0] as Either<AppException, List<ConversationPart>>;
      final bookmarkResult = results[1] as Either<AppException, List<Bookmark>>;

      if (contentResult.isLeft()) {
        final failure = contentResult.fold((l) => l, (r) => null);
        await crashlyticsService.recordError(
          failure!,
          StackTrace.current,
          reason: 'Failed to get conversation list',
          context: {
            'operation': 'getConversationList',
            'pathCount': _paths.length.toString(),
            'section': 'conversation',
          },
          fatal: false,
        );
        state = AsyncError(failure.message, StackTrace.current);
        return;
      }
      if (bookmarkResult.isLeft()) {
        final failure = bookmarkResult.fold((l) => l, (r) => null);
        await crashlyticsService.recordError(
          failure!,
          StackTrace.current,
          reason: 'Failed to get bookmarks',
          context: {
            'operation': 'getBookmarksBySection',
            'section': 'conversation',
          },
          fatal: false,
        );
        state = AsyncError(failure.message, StackTrace.current);
        return;
      }

      List<ConversationPart> contentData =
          contentResult.fold((l) => null, (r) => r)!;
      final List<Bookmark> bookmarkData =
          bookmarkResult.fold((l) => null, (r) => r)!;

      for (int i = 0; i < contentData.length; i++) {
        final item = contentData[i];
        final isBookmarked = bookmarkData.any(
          (bookmark) =>
              bookmark.path == _paths[i].contentPath && bookmark.isBookmarked,
        );
        contentData[i] = item.copyWith(isBookmarked: isBookmarked);
      }

      state = AsyncData(state.value!.copyWith(conversations: contentData));

      // Automatically select the current video after content is loaded
      if (contentData.isNotEmpty) {
        final currentIndex = state.value!.selectedIndex;
        await _initializeVideo(currentIndex);
      }
    } catch (error, stackTrace) {
      await crashlyticsService.recordError(
        error,
        stackTrace,
        reason: 'Unexpected error during content initialization',
        context: {
          'operation': 'initContent',
          'pathCount': _paths.length.toString(),
          'errorType': error.runtimeType.toString(),
        },
        fatal: false,
      );
      state = AsyncError(error.toString(), stackTrace);
    }
  }

  Future<void> _initializeVideo(int index) async {
    if (index < 0 || index >= state.value!.conversations.length) return;

    final videoUrl = state.value?.conversations[index].video;
    if (videoUrl == null || videoUrl.isEmpty) return;

    final subtitles = state.value?.conversations[index].subtitles;
    final subtitleUrl = getSubtitleUrl(subtitles);

    try {
      final videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(videoUrl),
      );

      // Load subtitles if available
      if (subtitleUrl != null && subtitleUrl.isNotEmpty) {
        await videoPlayerController.setClosedCaptionFile(
          _loadSubtitleFile(subtitleUrl),
        );
      }

      // Switch the video without saving as last course (for initial load)
      state.value?.flickManager?.handleChangeVideo(videoPlayerController);

      // Update the state to reflect the current video selection
      state = AsyncData(state.value!.copyWith(selectedIndex: index));
      isNextSection();
    } catch (e, stackTrace) {
      await crashlyticsService.recordError(
        e,
        stackTrace,
        reason: 'Failed to initialize video',
        context: {
          'videoIndex': index.toString(),
          'videoUrl': videoUrl,
          'hasSubtitles': subtitles != null,
          'subtitleUrl': subtitleUrl,
          'operation': '_initializeVideo',
          'errorType': e.runtimeType.toString(),
        },
        fatal: false,
      );
      // If video initialization fails, keep the current state but log the error
      // The UI will still show the temp video as fallback
      debugPrint('Failed to initialize video at index $index: $e');
    }
  }

  Future<void> selectVideo(int index) async {
    if (index < 0 || index >= state.value!.conversations.length) return;

    final videoUrl = state.value?.conversations[index].video ?? '';
    final subtitles = state.value?.conversations[index].subtitles;
    final subtitleUrl = getSubtitleUrl(subtitles);

    final videoPlayerController = VideoPlayerController.networkUrl(
      Uri.parse(videoUrl),
    );

    // Load subtitles if available
    if (subtitleUrl != null && subtitleUrl.isNotEmpty) {
      await videoPlayerController.setClosedCaptionFile(
        _loadSubtitleFile(subtitleUrl),
      );
    }

    state.value?.flickManager?.handleChangeVideo(videoPlayerController);

    List<ConversationPart> tempData = List.from(state.value!.conversations);
    tempData[index] = tempData[index].copyWith(
      videoMeta: tempData[index].videoMeta?.copyWith(isPlayed: true),
    );

    state = AsyncData(
      state.value!.copyWith(selectedIndex: index, conversations: tempData),
    );
    await saveLastCourse(index);
    isNextSection();
  }

  String? getSubtitleUrl(List<Subtitle>? subtitles) {
    if (subtitles == null) return null;
    final indexSubtitle = subtitles.indexWhere(
      (element) => element.lang == localName,
    );
    return subtitles[indexSubtitle].subtitleUrl;
  }

  Future<void> saveLastCourse(int index) async {
    final data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level,
      chapter: int.parse(chapter),
      section: SectionType.conversation,
      path: _paths[index].contentPath,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.conversation,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastConversation(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m)
    // Create a set of required paths and check if it's a subset of completed paths
    final requiredPaths =
        _paths.map((pathData) => pathData.contentPath).toSet();
    final allPathsCompleted = requiredPaths.difference(_completedPaths).isEmpty;

    if (allPathsCompleted) {
      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.conversation,
      );
    }
  }

  Future<void> saveResult() async {
    try {
      final result = await _userDataServiceRepository.saveLessonResult(
        level: level,
        chapter: chapter,
        section: SectionType.conversation,
        result: LessonResult(
          contentOrder: _paths[state.value!.selectedIndex].contentOrder,
          path: _paths[state.value!.selectedIndex].contentPath,
          result: {},
        ),
      );

      result.fold(
        (failure) async {
          await crashlyticsService.recordError(
            failure,
            StackTrace.current,
            reason: 'Failed to save lesson result',
            context: {
              'level': level,
              'chapter': chapter,
              'section': 'conversation',
              'contentPath': _paths[state.value!.selectedIndex].contentPath,
              'operation': 'saveLessonResult',
            },
            fatal: false,
          );
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) {
          _completedPaths.add(_paths[state.value!.selectedIndex].contentPath);
          _chapterContentStateNotifier.updateConversation(
            _paths[state.value!.selectedIndex],
          );
          markSectionAsCompleted();
        },
      );
    } catch (error, stackTrace) {
      await crashlyticsService.recordError(
        error,
        stackTrace,
        reason: 'Unexpected error saving lesson result',
        context: {
          'level': level,
          'chapter': chapter,
          'section': 'conversation',
          'selectedIndex': state.value!.selectedIndex.toString(),
          'operation': 'saveResult',
          'errorType': error.runtimeType.toString(),
        },
        fatal: false,
      );
      state = AsyncError(error.toString(), stackTrace);
    }
  }

  AsyncData<ConversationState> isNextSection() =>
      state = AsyncData(
        state.value!.copyWith(
          nextSection:
              state.value!.selectedIndex == _paths.length - 1 ? true : false,
        ),
      );

  Future<void> saveBookmark() async {
    try {
      bool isBookmarked =
          !state.value!.conversations[state.value!.selectedIndex].isBookmarked;

      final String partOrder =
          (_paths[state.value!.selectedIndex].partOrder ?? 0).toString();
      final String subpartOrder =
          (_paths[state.value!.selectedIndex].subpartOrder ?? 0).toString();
      final String contentOrder =
          (_paths[state.value!.selectedIndex].contentOrder).toString();
      final String speakingStage = '';
      //(_paths[state.value!.selectedIndex].speakingStage?.name ?? '');

      final String docId =
          '$level$chapter$partOrder$subpartOrder$contentOrder$speakingStage';
      final result = await _userDataServiceRepository.saveBookmark(
        section: SectionType.conversation,
        content: Bookmark(
          docId: docId,
          path: _paths[state.value!.selectedIndex].contentPath,
          isBookmarked: isBookmarked,
        ),
      );

      result.fold(
        (failure) async {
          await crashlyticsService.recordError(
            failure,
            StackTrace.current,
            reason: 'Failed to save bookmark',
            context: {
              'level': level,
              'chapter': chapter,
              'section': 'conversation',
              'docId': docId,
              'isBookmarked': isBookmarked.toString(),
              'contentPath': _paths[state.value!.selectedIndex].contentPath,
              'operation': 'saveBookmark',
            },
            fatal: false,
          );
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) {
          var conversations = state.value!.conversations;
          conversations = List.from(conversations);
          conversations[state.value!.selectedIndex] = conversations[state
                  .value!
                  .selectedIndex]
              .copyWith(isBookmarked: isBookmarked);
          state = AsyncData(
            state.value!.copyWith(conversations: conversations),
          );
          _paths[state.value!.selectedIndex] = _paths[state
                  .value!
                  .selectedIndex]
              .copyWith(isBookmarked: isBookmarked);
          _chapterContentStateNotifier.updateConversation(
            _paths[state.value!.selectedIndex],
          );
        },
      );
    } catch (error, stackTrace) {
      await crashlyticsService.recordError(
        error,
        stackTrace,
        reason: 'Unexpected error saving bookmark',
        context: {
          'level': level,
          'chapter': chapter,
          'section': 'conversation',
          'selectedIndex': state.value!.selectedIndex.toString(),
          'operation': 'saveBookmark',
          'errorType': error.runtimeType.toString(),
        },
        fatal: false,
      );
      state = AsyncError(error.toString(), stackTrace);
    }
  }
}
