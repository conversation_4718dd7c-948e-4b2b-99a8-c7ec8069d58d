// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pronunciation_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pronunciationControllerHash() =>
    r'eaabcf8583524a702ad2755ccebff63792319bf8';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$PronunciationController
    extends BuildlessAutoDisposeAsyncNotifier<PronunciationState> {
  late final String level;
  late final String chapter;
  late final String path;

  FutureOr<PronunciationState> build(String level, String chapter, String path);
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [PronunciationController].
@ProviderFor(PronunciationController)
const pronunciationControllerProvider = PronunciationControllerFamily();

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [PronunciationController].
class PronunciationControllerFamily
    extends Family<AsyncValue<PronunciationState>> {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [PronunciationController].
  const PronunciationControllerFamily();

  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [PronunciationController].
  PronunciationControllerProvider call(
    String level,
    String chapter,
    String path,
  ) {
    return PronunciationControllerProvider(level, chapter, path);
  }

  @override
  PronunciationControllerProvider getProviderOverride(
    covariant PronunciationControllerProvider provider,
  ) {
    return call(provider.level, provider.chapter, provider.path);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'pronunciationControllerProvider';
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [PronunciationController].
class PronunciationControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          PronunciationController,
          PronunciationState
        > {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [PronunciationController].
  PronunciationControllerProvider(String level, String chapter, String path)
    : this._internal(
        () =>
            PronunciationController()
              ..level = level
              ..chapter = chapter
              ..path = path,
        from: pronunciationControllerProvider,
        name: r'pronunciationControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$pronunciationControllerHash,
        dependencies: PronunciationControllerFamily._dependencies,
        allTransitiveDependencies:
            PronunciationControllerFamily._allTransitiveDependencies,
        level: level,
        chapter: chapter,
        path: path,
      );

  PronunciationControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.level,
    required this.chapter,
    required this.path,
  }) : super.internal();

  final String level;
  final String chapter;
  final String path;

  @override
  FutureOr<PronunciationState> runNotifierBuild(
    covariant PronunciationController notifier,
  ) {
    return notifier.build(level, chapter, path);
  }

  @override
  Override overrideWith(PronunciationController Function() create) {
    return ProviderOverride(
      origin: this,
      override: PronunciationControllerProvider._internal(
        () =>
            create()
              ..level = level
              ..chapter = chapter
              ..path = path,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        level: level,
        chapter: chapter,
        path: path,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    PronunciationController,
    PronunciationState
  >
  createElement() {
    return _PronunciationControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PronunciationControllerProvider &&
        other.level == level &&
        other.chapter == chapter &&
        other.path == path;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, level.hashCode);
    hash = _SystemHash.combine(hash, chapter.hashCode);
    hash = _SystemHash.combine(hash, path.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PronunciationControllerRef
    on AutoDisposeAsyncNotifierProviderRef<PronunciationState> {
  /// The parameter `level` of this provider.
  String get level;

  /// The parameter `chapter` of this provider.
  String get chapter;

  /// The parameter `path` of this provider.
  String get path;
}

class _PronunciationControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          PronunciationController,
          PronunciationState
        >
    with PronunciationControllerRef {
  _PronunciationControllerProviderElement(super.provider);

  @override
  String get level => (origin as PronunciationControllerProvider).level;
  @override
  String get chapter => (origin as PronunciationControllerProvider).chapter;
  @override
  String get path => (origin as PronunciationControllerProvider).path;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
