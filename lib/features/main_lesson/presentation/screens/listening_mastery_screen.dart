import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/listening_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/listening_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/image_soundwave.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/main_lesson_app_bar.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/dash_progress_indicator_question.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/dash_progress_indicator.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/custom_expansion_panel.dart';

class ListeningMasteryScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;

  const ListeningMasteryScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<ListeningMasteryScreen> createState() =>
      _ListeningMasteryScreenState();
}

class _ListeningMasteryScreenState extends ConsumerState<ListeningMasteryScreen>
    with TickerProviderStateMixin {
  late AsyncValue<ListeningState> viewState;
  late ListeningController viewModel;
  late final AudioPlayer _player;
  // To track the URL currently set in _player, to avoid redundant setSource calls
  String? _lastSetAudioUrl;

  late final AudioPlayer _feedbackPlayer;
  bool _isAnimationComplete = false;
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  // This controller is used for horizontal scrolling of questions
  final ScrollController _scrollController = ScrollController();

  static const _transitionDuration = Duration(seconds: 1);

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer()..setReleaseMode(ReleaseMode.stop);
    _feedbackPlayer = AudioPlayer()..setReleaseMode(ReleaseMode.stop);

    _player.setAudioContext(
      AudioContext(
        android: AudioContextAndroid(
          isSpeakerphoneOn: false,
          stayAwake: true,
          contentType: AndroidContentType.music,
          usageType: AndroidUsageType.media,
          audioFocus: AndroidAudioFocus.gain,
        ),
        // iOS: AudioContextIOS(
        //   category: AVAudioSessionCategory.playback,
        //   options: {},
        // ),
      ),
    );

    _feedbackPlayer.setAudioContext(
      AudioContext(
        android: AudioContextAndroid(
          isSpeakerphoneOn: false,
          stayAwake: false,
          contentType: AndroidContentType.sonification,
          usageType: AndroidUsageType.notificationEvent,
          audioFocus: AndroidAudioFocus.gainTransientMayDuck,
        ),
        // iOS: AudioContextIOS(
        //   category: AVAudioSessionCategory.ambient,
        //   options: {AVAudioSessionOptions.mixWithOthers},
        // ),
      ),
    );

    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: _transitionDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    )..addStatusListener(_handleAnimationStatus);
    _animationController.forward();
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_isAnimationComplete) {
      _animationController.reverse();
    } else if (status == AnimationStatus.dismissed) {
      if (mounted) {
        setState(() => _isAnimationComplete = true);
      }
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _player.dispose();
    _feedbackPlayer.dispose();
    super.dispose();
  }

  void _updateAudioSource(String? newUrl) {
    // newUrl can now be nullable
    final urlToSet =
        newUrl ?? ''; // Use empty string if null, for consistent handling

    if (urlToSet.isNotEmpty && urlToSet != _lastSetAudioUrl) {
      _player
          .setSource(UrlSource(urlToSet))
          .then((_) {
            _lastSetAudioUrl = urlToSet;
          })
          .catchError((e, s) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Error loading new audio: $e")),
              );
            }
          });
    } else if (urlToSet.isEmpty && _lastSetAudioUrl != null) {
      _player.stop(); // Ensure player is stopped if URL becomes empty
      _lastSetAudioUrl = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final prov = listeningControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    ref.listen(prov.select((value) => value), ((previous, next) {
      next.maybeWhen(
        error: (error, track) {
          if (error.toString().contains('Please answer')) {
            return;
          }
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        data: (data) {
          if (data.listenings.isEmpty ||
              data.currentPage >= data.listenings.length) {
            return;
          }
          // Check if all questions are answered and we haven't started navigation yet
          final allAnswered = data.currentListenings.questions.every(
            (q) => q.answer != null,
          );
          if (allAnswered) {
            _player.pause();
            viewModel.saveResult();
            Future.delayed(const Duration(milliseconds: 500)).then((e) {
              customNav(
                context,
                RouterName.listeningMasteryResult,
                isReplace: true,
                params: {
                  'level': widget.level,
                  'chapter': widget.chapter,
                  'path': widget.path,
                },
              );
            }); // Optional delay for UX
          }
        },
        orElse: () {},
      );
    }));

    // This ref.listen is now the primary way the audio source is set and updated.
    // It will trigger on initial load and whenever the relevant state changes.
    ref.listen<String?>(
      prov.select((asyncValue) {
        final value = asyncValue.valueOrNull;
        if (value != null &&
            value.listenings.isNotEmpty &&
            value.currentPage < value.listenings.length) {
          return value.listenings[value.currentPage].main;
        }
        return null; // Return null if no valid URL
      }),
      (prevUrl, newUrl) {
        // _updateAudioSource will handle if newUrl is null, empty or same as before.
        _updateAudioSource(newUrl);
      },
      // Optional: fireImmediately can be useful if you need it to run once with the initial value
      // fireImmediately: true, // Consider if needed, usually ref.watch handles initial state
    );

    final currentAudioUrl =
        viewState.whenData((data) {
          if (data.listenings.isNotEmpty &&
              data.currentPage < data.listenings.length) {
            return data.listenings[data.currentPage].main;
          }
          return null;
        }).valueOrNull;

    _updateAudioSource(currentAudioUrl);

    return switch (viewState) {
      AsyncData(:final value) =>
        value.isNewPart && !_isAnimationComplete
            ? _buildTransition(value)
            : _buildBody(value),
      AsyncError(:final error) => _buildBody(null, error: error),
      AsyncLoading() => const AppLoading(),
      _ => const Scaffold(body: Center(child: Text('Initializing...'))),
    };
  }

  Widget _buildTransition(ListeningState stateValue) => FadeTransition(
    opacity: _animation,
    child: Scaffold(
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 46),
        child: ListView(
          children: [
            const SizedBox(height: 82),
            if (stateValue.listenings.isNotEmpty) ...[
              if (stateValue.listenings.length > 1)
                Text(
                  '${context.loc.exercise} ${stateValue.currentPage + 1}',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              const SizedBox(height: 42),
              _buildTransitionImage(),
              const SizedBox(height: 24),
              Text(
                stateValue.listenings.isNotEmpty &&
                        stateValue.currentPage < stateValue.listenings.length
                    ? stateValue.listenings[stateValue.currentPage].title ?? ''
                    : '',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ],
        ),
      ),
    ),
  );

  Widget _buildTransitionImage() => Container(
    height: 252,
    width: 252,
    decoration: const BoxDecoration(
      shape: BoxShape.circle,
      gradient: LinearGradient(
        colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
        begin: Alignment.bottomLeft,
        end: Alignment.topRight,
      ),
      image: DecorationImage(
        image: AssetImage(
          '$assetImageMainLesson/listening_mastery/BG35-Android.png',
        ),
        fit: BoxFit.scaleDown,
      ),
    ),
  );

  Widget _buildBody(ListeningState? stateValue, {Object? error}) => Scaffold(
    appBar:
        stateValue != null && stateValue.listenings.isNotEmpty
            ? MainLessonAppBar(
              key: const Key('sticky_header'),
              title: context.loc.listeningMastery,
              isBookmarked:
                  viewState.value!.listenings.isNotEmpty
                      ? viewState
                          .value!
                          .listenings[viewState.value!.currentPage]
                          .isBookmarked
                      : false,
              onBookmark: () {
                viewModel.saveBookmark();
              },
              onHelp: () {
                customNav(
                  context,
                  RouterName.listeningMasteryInstruction,
                  isReplace: true,
                  params: {'level': widget.level, 'chapter': widget.chapter},
                );
              },
            )
            : null,
    body: Stack(
      alignment: Alignment.center,
      children: [
        if (stateValue != null && stateValue.listenings.isNotEmpty)
          SingleChildScrollView(
            child: SizedBox(
              height: MediaQuery.of(context).size.height,
              child: _buildListContent(stateValue),
            ),
          )
        else if (error != null)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                "Error loading content: ${error.toString()}",
                textAlign: TextAlign.center,
              ),
            ),
          )
        else if (!viewState.isLoading && stateValue == null)
          Center(child: Text("No content available.")),
      ],
    ),
  );

  Widget _buildListContent(ListeningState stateValue) => SafeArea(
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 24),
        DashProgressIndicator(
          progress: stateValue.currentPage,
          totalLength: stateValue.listenings.length,
        ),
        const SizedBox(height: 24),
        ImageSoundWave(
          imageUrl: stateValue.currentListenings.image ?? '',
          audioUrl: stateValue.currentListenings.main ?? '',
          isAudioEnabled: ref.watch(audioToggleProvider),
          isLandscape: true,
        ),
        const SizedBox(height: 16),
        DashProgressIndicatorQuestion(
          questions: stateValue.currentListenings.questions,
        ),
        const SizedBox(height: 12),
        Expanded(child: _buildQuestionsList(stateValue)),
      ],
    ),
  );

  Widget _buildQuestionsList(ListeningState stateValue) {
    final currentListeningPart = stateValue.currentListenings;
    final questions = currentListeningPart.questions;
    final isAudioEnabled = ref.read(audioToggleProvider);

    if (questions.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: const Center(child: Text("No questions available.")),
      );
    }
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        controller: _scrollController,
        primary: false,
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemCount: questions.length,
        itemBuilder: (context, index) {
          return Container(
            width: MediaQuery.of(context).size.width - 32,
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xffE82329).withValues(alpha: .4),
                width: 0.4,
              ),
            ),
            child: _buildQuestionItemWithScroll(
              stateValue,
              index,
              isAudioEnabled,
              _scrollController,
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuestionItemWithScroll(
    ListeningState stateValue,
    int index,
    bool isAudioEnabled,
    ScrollController scrollController,
  ) {
    final questions = stateValue.currentListenings.questions;
    final question = questions[index];
    final isAnswered = question.answer != null;
    final isCurrentlyExpanded =
        stateValue.expandedQuestionIndex == index && !isAnswered;

    return CustomExpansionPanel(
      key: ValueKey('question_$index'),
      question: question,
      isExpanded: isCurrentlyExpanded,
      isAnswered: isAnswered,
      isCorrect: question.isCorrect,
      indexToAlphabet: viewModel.indexToAlphabet,
      onHeaderTap: () {
        if (!isAnswered) {
          viewModel.setExpandedQuestion(index);
        }
      },
      onChoiceSelected: (String selectedChoiceValue) async {
        final currentQuestion = stateValue.currentListenings.questions[index];
        final choiceIndex = viewModel.alphabetToIndex(selectedChoiceValue);
        bool isCorrectAnswer = false;
        if (choiceIndex != -1 && choiceIndex < currentQuestion.choices.length) {
          isCorrectAnswer =
              currentQuestion.choices[choiceIndex].isCorrect ?? false;
        }

        if (isAudioEnabled) {
          await _feedbackPlayer.stop();
          if (isCorrectAnswer) {
            _feedbackPlayer.play(AssetSource('sounds/correct.mp3'));
          } else {
            _feedbackPlayer.play(AssetSource('sounds/wrong.mp3'));
          }
        }

        viewModel.selectAnswer(
          questionIndex: index,
          value: selectedChoiceValue,
        );

        // Scroll to next question after answering
        if (index < questions.length - 1) {
          Future.delayed(const Duration(milliseconds: 300), () {
            scrollController.animateTo(
              (MediaQuery.of(context).size.width - 32 + 16) * (index + 1),
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeInOut,
            );
          });
        }
      },
    );
  }
}
