// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'questionnaire_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$questionnaireControllerHash() =>
    r'556f8b90a8e3a0ff8eccf167d087885232613763';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [QuestionnaireController].
@ProviderFor(QuestionnaireController)
final questionnaireControllerProvider = AutoDisposeAsyncNotifierProvider<
  QuestionnaireController,
  QuestionnaireState
>.internal(
  QuestionnaireController.new,
  name: r'questionnaireControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$questionnaireControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QuestionnaireController =
    AutoDisposeAsyncNotifier<QuestionnaireState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
