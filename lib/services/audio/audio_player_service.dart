import 'dart:async';
import 'package:audioplayers/audioplayers.dart' as ap;

/// Service interface for audio player operations, allowing mocking in tests.
abstract class AudioPlayerService {
  Stream<ap.PlayerState> get onPlayerStateChanged;
  Stream<Duration> get onDurationChanged;
  Stream<Duration> get onPositionChanged;
  Stream<void> get onPlayerComplete;

  Future<void> play(ap.UrlSource source);
  Future<void> pause();
  Future<void> resume();
  Future<void> stop();
  Future<void> seek(Duration position);
  Future<void> dispose();
}

/// Real implementation of AudioPlayerService using audioplayers.
class RealAudioPlayerService implements AudioPlayerService {
  late final ap.AudioPlayer _audioPlayer;

  RealAudioPlayerService() {
    _audioPlayer = ap.AudioPlayer();
  }

  @override
  Stream<ap.PlayerState> get onPlayerStateChanged =>
      _audioPlayer.onPlayerStateChanged;

  @override
  Stream<void> get onPlayerComplete =>
      _audioPlayer.onPlayerComplete.map((_) => null);

  @override
  Stream<Duration> get onDurationChanged => _audioPlayer.onDurationChanged;

  @override
  Stream<Duration> get onPositionChanged => _audioPlayer.onPositionChanged;

  @override
  Future<void> play(ap.UrlSource source) => _audioPlayer.play(source);

  @override
  Future<void> pause() => _audioPlayer.pause();

  @override
  Future<void> resume() => _audioPlayer.resume();

  @override
  Future<void> stop() => _audioPlayer.stop();

  @override
  Future<void> seek(Duration position) => _audioPlayer.seek(position);

  @override
  Future<void> dispose() => _audioPlayer.dispose();
}
