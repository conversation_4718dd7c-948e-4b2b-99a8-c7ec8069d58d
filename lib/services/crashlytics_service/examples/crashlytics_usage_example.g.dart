// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crashlytics_usage_example.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$exampleControllerHash() => r'a9b609f33379f852b64ef1217c858b79ea674f76';

/// Example of using the Crashlytics service in a typical controller
///
/// Copied from [ExampleController].
@ProviderFor(ExampleController)
final exampleControllerProvider =
    AutoDisposeNotifierProvider<ExampleController, String>.internal(
      ExampleController.new,
      name: r'exampleControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$exampleControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ExampleController = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
